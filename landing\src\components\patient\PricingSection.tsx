"use client";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

const PricingSection = () => {
  const freePlanFeatures = [
    "Symptoms Contextualizer (basic use)",
    "Pain & Fatigue Tracker (daily check-ins)",
    "Access to blog"
  ];

  const premiumPlanFeatures = [
    "All Free Features",
    "Advanced AI insights",
    "Medication & Supplement Optimizer",
    "Personalized care recommendations",
    "Expert health summary for your doctor"
  ];

  const providerPlanFeatures = [
    "Dashboard to manage multiple patients",
    "Share insights & collaborate with patients",
    "Provider-grade AI tools",
    "Priority support",
    "Early access to new features"
  ];

  return (
    <section className="py-16 sm:py-8 md:py-12 lg:py-16 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-[#2E475D] mb-4">
            Pricing
          </h2>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">

          {/* Free Plan */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm flex flex-col h-full">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold text-[#2E475D] mb-2">Free Plan</h3>
            </div>

            <div className="mb-6 flex-grow">
              {freePlanFeatures.map((feature, index) => (
                <div key={index} className="flex items-start gap-3 mb-3">
                  <div className="w-1.5 h-1.5 bg-[#6A8E99] rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-700">{feature}</p>
                </div>
              ))}
            </div>

            <div className="text-center mt-auto">
              <Button
                variant="outline"
                className="w-full bg-[#6A8E99] hover:bg-[#5a7a84] text-white border-[#6A8E99] py-2 px-4 rounded-md"
              >
                Get Started Free
              </Button>
            </div>
          </div>

          {/* Premium Plan (Patients) */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm flex flex-col h-full">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold text-[#2E475D] mb-2">Premium Plan (Patients)</h3>
            </div>

            <div className="mb-6 flex-grow">
              {premiumPlanFeatures.map((feature, index) => (
                <div key={index} className="flex items-start gap-3 mb-3">
                  <div className="w-1.5 h-1.5 bg-[#6A8E99] rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-700">{feature}</p>
                </div>
              ))}
            </div>

            <div className="text-center mb-4">
              <div className="text-2xl font-bold text-[#2E475D] mb-1">$10/month</div>
              <p className="text-sm text-gray-600">Free 7-day trial available</p>
            </div>

            <div className="text-center mt-auto">
              <Button
                className="w-full bg-[#6A8E99] hover:bg-[#5a7a84] text-white py-2 px-4 rounded-md"
              >
                Start Free Trial
              </Button>
            </div>
          </div>

          {/* Provider (Custom) */}
          <div className="bg-white border border-gray-200 rounded-lg p-6 shadow-sm flex flex-col h-full">
            <div className="text-center mb-6">
              <h3 className="text-xl font-semibold text-[#2E475D] mb-2">Provider (Custom)</h3>
            </div>

            <div className="mb-6 flex-grow">
              {providerPlanFeatures.map((feature, index) => (
                <div key={index} className="flex items-start gap-3 mb-3">
                  <div className="w-1.5 h-1.5 bg-[#6A8E99] rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm text-gray-700">{feature}</p>
                </div>
              ))}
            </div>

            <div className="text-center mb-4">
              <p className="text-sm text-gray-600 italic">Custom pricing (based on clinic size or users)</p>
            </div>

            <div className="text-center mt-auto">
              <Button
                variant="outline"
                className="w-full bg-[#6A8E99] hover:bg-[#5a7a84] text-white py-2 px-4 rounded-md"
              >
                Message Us
              </Button>
            </div>
          </div>

        </div>
      </div>
    </section>
  );
};

export default PricingSection;