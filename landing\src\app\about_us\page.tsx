"use client";

import Image from "next/image";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import Footer from "@/components/Footer";
import { useState } from "react";

export default function AboutUsPage() {
  const [formData, setFormData] = useState({
    fullName: "",
    emailAddress: "",
    subject: "",
    message: ""
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    console.log("Form submitted:", formData);
  };

  return (
    <div className="min-h-screen bg-white">
      {/* About Us Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="flex flex-col justify-center">
              <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-[#2E475D] mb-6">
                About Us
              </h1>
              <div className="space-y-4 text-gray-600 text-base leading-relaxed">
                <p>
                  Petals Health is a first-of-its-kind AI + telehealth platform that helps you understand your health in real-time using AI built on clinical data.
                </p>
                <p>
                  We help you optimize your medication and supplements with intelligent recommendations, track symptoms, health patterns and treatment progress - all in one dashboard.
                </p>
                <p>
                  With HIPAA and GDPR-compliant privacy & security, we ensure your health data is protected while providing you with the insights you need to take control of your health.
                </p>
              </div>
            </div>
            <div className="flex justify-center lg:justify-end">
              <div className="relative w-full max-w-md lg:max-w-lg h-[350px] lg:h-[450px]">
                <Image
                  src="/images/about_p_01.svg"
                  alt="Healthcare professional"
                  fill
                  style={{ objectFit: 'contain' }}
                  priority
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Our Mission Section */}
      <section className="py-16 bg-[#F2FFF6]">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="flex justify-center lg:justify-start order-2 lg:order-1">
              <div className="relative w-full max-w-md lg:max-w-lg h-[350px] lg:h-[400px]">
                <Image
                  src="/images/about_p_02.svg"
                  alt="Our mission"
                  fill
                  style={{ objectFit: 'contain' }}
                />
              </div>
            </div>
            <div className="flex flex-col justify-center order-1 lg:order-2">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#2E475D] mb-6">
                Our Mission
              </h2>
              <div className="space-y-4 text-gray-600 text-base leading-relaxed">
                <p>
                  To democratize access to intelligent, personalized health guidance that empowers individuals to make informed decisions about their health and wellbeing.
                </p>
                <p>
                  We believe that everyone deserves access to high-quality healthcare guidance, regardless of their location, schedule, or economic status. Through innovative AI technology and compassionate care, we're making this vision a reality.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What We Do Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="flex flex-col justify-center">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#2E475D] mb-6">
                What We Do
              </h2>
              <div className="space-y-4 text-gray-600 text-base leading-relaxed">
                <p>
                  We create AI-powered solutions that bridge the gap between patients and healthcare providers. Our platform combines cutting-edge artificial intelligence with evidence-based medical knowledge to provide personalized health insights.
                </p>
                <p>
                  From symptom tracking and medication management to health pattern analysis and treatment optimization, we provide comprehensive tools that support both patients and healthcare providers in delivering better outcomes.
                </p>
              </div>
            </div>
            <div className="flex justify-center lg:justify-end">
              <div className="relative w-full max-w-md lg:max-w-lg h-[350px] lg:h-[400px]">
                <Image
                  src="/images/about_p_03.svg"
                  alt="What we do"
                  fill
                  style={{ objectFit: 'contain' }}
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Why We Exist Section */}
      <section className="py-16 bg-[#F2FFF6]">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 items-center">
            <div className="flex justify-center lg:justify-start order-2 lg:order-1">
              <div className="relative w-full max-w-md lg:max-w-lg h-[350px] lg:h-[400px]">
                <Image
                  src="/images/about_p_04.svg"
                  alt="Why we exist"
                  fill
                  style={{ objectFit: 'contain' }}
                />
              </div>
            </div>
            <div className="flex flex-col justify-center order-1 lg:order-2">
              <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#2E475D] mb-6">
                Why We Exist
              </h2>
              <div className="space-y-4 text-gray-600 text-base leading-relaxed">
                <p>
                  Healthcare is complex, fragmented, and often inaccessible. Patients struggle to understand their conditions, manage medications, and navigate the healthcare system effectively.
                </p>
                <p>
                  We exist to simplify this complexity, providing clear, actionable insights that help people take control of their health journey. Our goal is to make quality healthcare guidance accessible to everyone, everywhere.
                </p>
                <p>
                  Through technology and innovation, we're building a future where healthcare is proactive, personalized, and accessible to all.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Us Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-[#2E475D] mb-4">
              Contact Us
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto mb-2">
              We're Here to Help — Anytime
            </p>
            <p className="text-gray-600 max-w-2xl mx-auto mb-2">
              Have questions? Feedback? Want to partner with us?
            </p>
            <p className="text-gray-600 max-w-2xl mx-auto mb-4">
              Whether you're a user, health professional, or investor — we'd love to hear from you.
            </p>
            <p className="text-gray-600 max-w-2xl mx-auto font-medium">
              Get in Touch
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">

            {/* Contact Information */}
            <div className="space-y-8">
              <div>
                <h3 className="text-lg font-semibold text-[#2E475D] mb-4">Email:</h3>
                <div className="text-gray-600">
                  <p className="mb-2">Prefer to write to us directly? Send an email to:</p>
                  <p className="text-blue-600 font-medium">[<EMAIL>]</p>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-[#2E475D] mb-4">Contact Form:</h3>
                <div className="text-gray-600">
                  <p>Fill out the quick form and our team will get back to you within 24-48 hours.</p>
                </div>
              </div>
            </div>

            {/* Contact Form */}
            <Card className="p-8 border-gray-200">
              <CardContent className="p-0">
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <label htmlFor="fullName" className="block text-sm font-medium text-[#2E475D] mb-2">
                      Full name
                    </label>
                    <Input
                      type="text"
                      id="fullName"
                      name="fullName"
                      value={formData.fullName}
                      onChange={handleInputChange}
                      placeholder="Adebayo Ademola"
                      required
                      className="w-full"
                    />
                  </div>

                  <div>
                    <label htmlFor="emailAddress" className="block text-sm font-medium text-[#2E475D] mb-2">
                      Email Address
                    </label>
                    <Input
                      type="email"
                      id="emailAddress"
                      name="emailAddress"
                      value={formData.emailAddress}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      required
                      className="w-full"
                    />
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-[#2E475D] mb-2">
                      Subject
                    </label>
                    <Select value={formData.subject} onValueChange={(value) => setFormData(prev => ({ ...prev, subject: value }))}>
                      <SelectTrigger className="w-full">
                        <SelectValue placeholder="General Inquiry" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="general">General Inquiry</SelectItem>
                        <SelectItem value="support">Support</SelectItem>
                        <SelectItem value="partnership">Partnership</SelectItem>
                        <SelectItem value="investment">Investment</SelectItem>
                        <SelectItem value="feedback">Feedback</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-[#2E475D] mb-2">
                      Message
                    </label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder=""
                      rows={6}
                      className="w-full resize-none"
                    />
                    <p className="text-xs text-gray-500 mt-1">Min. 10 words</p>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-[#7BA3A3] hover:bg-[#7BA3A3]/90 text-white py-3 rounded-md"
                  >
                    Submit
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <Footer />
    </div>
  );
}