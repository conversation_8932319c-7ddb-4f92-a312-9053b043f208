"use client";

const WhatIsPetalsSection = () => {
  const features = [
    {
      title: 'Understand your health in real-time using AI built on clinical data.',
      width: 'w-full sm:w-[35%]',
    },
    {
      title: 'Optimize your medication and supplements with intelligent recommendations',
      width: 'w-full sm:w-[50%]',
    },
    {
      title: 'Track symptoms, health patterns and treatment progress- all in one dashboard',
      width: 'w-full sm:w-[50%]',
    },
    {
      title: 'HIPAA and GDPR-compliant Privacy & Security',
      width: 'w-full sm:w-[35%]',
    },
  ];

  return (
    <section className="py-12 sm:py-16 md:py-20 lg:py-24 bg-white">
      <div className="container mx-auto px-4 sm:px-6 lg:px-28">
        <div className="flex flex-col lg:flex-row items-center gap-8">
          <div className="w-full lg:w-1/2">
            <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-gray-900 mb-4">What is petals Health Ai?</h2>
            <p className="text-gray-600 mb-8">
              Petals Health is a first-of-its- kind AI + telehealth platform that help you
            </p>

            <div className="flex flex-wrap justify-center sm:justify-start gap-4 mx-auto">
              {features.map((feature, index) => (
                <div key={index} className={`bg-white rounded-xl shadow-sm p-6 border border-gray-100 ${feature.width}`}>
                  <p className="text-gray-800">{feature.title}</p>
                </div>
              ))}
            </div>

            <button className="mt-8 px-6 py-3 bg-[#6A8E99] hover:bg-[#5a7a84] text-white rounded-lg transition-colors w-full lg:w-40 sm:mx-auto">
              Read More
            </button>
          </div>

          <div className="w-full lg:w-1/2">
            <img
              src="/images/whatIsPHA.svg"
              alt="Petals Health AI in action"
              className="w-full h-auto rounded-lg"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhatIsPetalsSection; 